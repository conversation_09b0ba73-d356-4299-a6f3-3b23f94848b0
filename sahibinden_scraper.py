import time
import random
import csv
import os
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import undetected_chromedriver as uc  # KRITIK: test.py'deki gibi
from typing import Optional
import undetected_chromedriver as uc
import logging

# Logging ayarları
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SahibindenScraper:
    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.scraped_data = []
        self.visited_urls = set()
        
    def setup_driver(self):
        """test_chrome.py'deki başarılı yaklaşımla bot detection bypass"""
        logger.info("Chrome başlatılıyor (test_chrome.py tarzı)...")

        try:
            # test.py'deki AYNI options ama Chrome path olmadan
            options = uc.ChromeOptions()
            options.add_argument("--guest")  # misafir modu - KRITIK!
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument(
                "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/139.0.0.0 Safari/537.36"
            )

            # test.py'deki AYNI undetected_chromedriver
            self.driver = uc.Chrome(options=options)
            self.driver.maximize_window()

            # test.py'deki AYNI CDP komutları
            self.driver.execute_cdp_cmd(
                "Network.setExtraHTTPHeaders",
                {"headers": {"Accept-Language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7"}}
            )

            logger.info("🚀 Chrome driver başlatıldı (undetected + guest mod)")

        except Exception as e:
            logger.error(f"Chrome başlatılamadı: {e}")
            raise
        
    def random_wait(self, min_seconds=4, max_seconds=7):
        """Rastgele bekleme süresi"""
        wait_time = random.uniform(min_seconds, max_seconds)
        logger.info(f"{wait_time:.2f} saniye bekleniyor...")
        time.sleep(wait_time)

    def human_like_behavior(self):
        """İnsan gibi davranış simülasyonu"""
        try:
            # Random mouse movements
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                ActionChains(self.driver).move_by_offset(x, y).perform()
                time.sleep(random.uniform(0.1, 0.3))
                ActionChains(self.driver).move_by_offset(-x, -y).perform()
                time.sleep(random.uniform(0.1, 0.3))

            # Random scroll
            scroll_amount = random.randint(100, 300)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            time.sleep(random.uniform(0.5, 1))
            self.driver.execute_script(f"window.scrollBy(0, -{scroll_amount//2});")

        except Exception as e:
            logger.warning(f"İnsan gibi davranış simülasyonu hatası: {e}")

    def wait_for_page_load(self):
        """Sayfa yüklenmesini bekle ve insan gibi davran"""
        # Sayfa yüklenmesini bekle
        time.sleep(random.uniform(3, 6))

        # İnsan gibi davranış
        self.human_like_behavior()

        # Biraz daha bekle
        time.sleep(random.uniform(1, 3))

    def bypass_cloudflare(self):
        """test_chrome.py'deki AYNI Cloudflare bypass kodu"""
        if not self.driver:
            return

        # test_chrome.py'deki AYNI loop
        for i in range(30):  # 30 saniye boyunca kontrol et
            time.sleep(1)
            title = self.driver.title
            logger.info(f"Saniye {i+1}: {title}")

            # test_chrome.py'deki AYNI iframe kontrolü
            try:
                # Iframe'leri kontrol et
                iframes = self.driver.find_elements(By.CSS_SELECTOR, "iframe")
                logger.info(f"{len(iframes)} iframe bulundu")

                for iframe in iframes:
                    src = iframe.get_attribute("src")
                    if src and ("turnstile" in src or "cloudflare" in src):
                        logger.info(f"Cloudflare iframe bulundu: {src}")

                        # Iframe'e geç
                        self.driver.switch_to.frame(iframe)

                        # Checkbox'ı ara
                        checkbox_selectors = [
                            "input[type='checkbox']",
                            "label",
                            "div[role='checkbox']",
                            ".cb-i",
                            "#challenge-stage"
                        ]

                        for selector in checkbox_selectors:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            if elements:
                                logger.info(f"Checkbox bulundu: {selector}")
                                elements[0].click()
                                logger.info("Checkbox tıklandı!")
                                time.sleep(3)
                                break

                        # Ana frame'e geri dön
                        self.driver.switch_to.default_content()
                        break

                # test_chrome.py'deki AYNI direkt sayfa kontrolü
                if not iframes:
                    checkbox_selectors = [
                        "input[type='checkbox']",
                        ".cf-turnstile",
                        "#challenge-form",
                        "[data-testid='cf-turnstile']"
                    ]

                    for selector in checkbox_selectors:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            logger.info(f"Direkt checkbox bulundu: {selector}")
                            elements[0].click()
                            logger.info("Checkbox tıklandı!")
                            time.sleep(3)
                            break

            except Exception as e:
                logger.info(f"Checkbox tıklama hatası: {e}")

            # test_chrome.py'deki AYNI çıkış kontrolü
            if "Bir dakika lütfen" not in title and "Just a moment" not in title:
                logger.info("Cloudflare geçildi!")
                return True

        # test_chrome.py'deki AYNI else bloğu
        logger.info("Cloudflare geçilemedi, ama devam ediliyor...")
        return False
        
    def scroll_page_slightly(self):
        """Sayfayı hafifçe aşağı kaydır"""
        if not self.driver:
            return
        scroll_amount = random.randint(200, 500)
        self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
        time.sleep(random.uniform(1, 2))
        
    def get_fresh_listing_elements(self):
        """Her seferinde fresh ilan elementlerini al - stale element sorununu çözer"""
        if not self.driver:
            return []

        try:
            # Sayfa yüklenme kontrolü
            time.sleep(2)

            # Fresh elementleri al - basit selector
            listing_elements = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/ilan/']")

            # Görünür ve tıklanabilir olanları filtrele
            clickable_elements = []
            for element in listing_elements:
                try:
                    if element.is_displayed() and element.is_enabled():
                        href = element.get_attribute("href")
                        if href and "/ilan/" in href:  # /detay kontrolünü kaldırdım
                            clickable_elements.append(element)
                except:
                    continue

            logger.info(f"✅ {len(clickable_elements)} fresh ilan elementi bulundu")
            return clickable_elements

        except Exception as e:
            logger.error(f"Fresh ilan elementlerini alma hatası: {e}")
            return []
            
    def extract_listing_details(self):
        """İlan detaylarını çek - Detay sayfasından tam bilgiler"""
        if not self.driver:
            return None

        try:
            # Sayfa yüklenmesini bekle - WebDriverWait kullan
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "h1"))
                )
                logger.info("✅ Sayfa yüklendi")
            except TimeoutException:
                logger.warning("⚠️ Sayfa yüklenme timeout")

            # Sayfa yüklenme kontrolü
            current_url = self.driver.current_url
            logger.info(f"İlan detay sayfası: {current_url}")

            # Giriş sayfasına yönlendirildi mi kontrol et
            if "giris" in current_url.lower() or "login" in current_url.lower():
                logger.error("❌ Giriş sayfasına yönlendirildi!")
                return None

            listing_data = {}

            # İlan başlığı - h1
            try:
                title_element = self.driver.find_element(By.CSS_SELECTOR, "h1")
                title = title_element.text.strip()
                listing_data['baslik'] = title
                logger.info(f"✅ Başlık: {title[:50]}...")
            except Exception as e:
                logger.warning(f"❌ Başlık bulunamadı: {e}")
                listing_data['baslik'] = "N/A"

            # Fiyat - h3
            try:
                price_element = self.driver.find_element(By.CSS_SELECTOR, "h3")
                price_text = price_element.text.strip()

                if "TL" in price_text:
                    price = price_text.split("Kredi")[0].strip()
                    listing_data['fiyat'] = price
                    logger.info(f"✅ Fiyat: {price}")
                else:
                    listing_data['fiyat'] = price_text

            except Exception as e:
                logger.warning(f"❌ Fiyat bulunamadı: {e}")
                listing_data['fiyat'] = "N/A"

            # Konum - h2
            try:
                location_element = self.driver.find_element(By.CSS_SELECTOR, "h2")
                location = location_element.text.strip()
                listing_data['konum'] = location
                logger.info(f"✅ Konum: {location}")
            except Exception as e:
                logger.warning(f"❌ Konum bulunamadı: {e}")
                listing_data['konum'] = "N/A"
                
            # İlan detayları listesi
            try:
                detail_list = self.driver.find_elements(By.CSS_SELECTOR, "ul li")
                
                for item in detail_list:
                    try:
                        strong_element = item.find_element(By.CSS_SELECTOR, "strong")
                        key = strong_element.text.replace(":", "").strip()
                        
                        # Strong elementinden sonraki metni al
                        value = item.text.replace(strong_element.text, "").strip()
                        
                        if key and value:
                            listing_data[key.lower().replace(" ", "_")] = value
                            
                    except NoSuchElementException:
                        continue
                        
            except:
                logger.warning("İlan detayları alınamadı")
                
            # İletişim bilgileri - Ad Soyad ve Telefon
            try:
                # Ad Soyad - h5 elementinde
                contact_name = self.driver.find_element(By.CSS_SELECTOR, "h5")
                ad_soyad = contact_name.text.strip()
                listing_data['ad_soyad'] = ad_soyad
                logger.info(f"✅ Ad Soyad: {ad_soyad}")

            except Exception as e:
                logger.warning(f"❌ Ad Soyad bulunamadı: {e}")
                listing_data['ad_soyad'] = "N/A"

            # Telefon numarası
            try:
                telefon = "N/A"

                # Tüm li elementlerini kontrol et
                phone_elements = self.driver.find_elements(By.CSS_SELECTOR, "ul li")
                for element in phone_elements:
                    element_text = element.text
                    if "Cep" in element_text or "Tel" in element_text:
                        # Telefon numarasını çıkar
                        telefon = element_text.replace("Cep", "").replace("Tel", "").strip()
                        break

                listing_data['telefon'] = telefon
                logger.info(f"✅ Telefon: {telefon}")

            except Exception as e:
                logger.warning(f"❌ Telefon bulunamadı: {e}")
                listing_data['telefon'] = "N/A"

            # URL'yi ekle
            listing_data['url'] = self.driver.current_url
            
            logger.info(f"İlan detayları çekildi: {listing_data.get('baslik', 'N/A')}")
            return listing_data

        except Exception as e:
            logger.error(f"İlan detayları çekilirken hata: {e}")
            return None

    def extract_listing_from_row(self, row):
        """Liste sayfasındaki ilan satırından bilgileri çek + GİZLİ BİLGİLER ARA"""
        try:
            cells = row.find_elements(By.CSS_SELECTOR, "td")

            if len(cells) < 7:
                logger.warning("Yetersiz cell sayısı")
                return None

            listing_data = {}

            # İlan başlığı - 2. cell'deki link
            try:
                title_link = cells[1].find_element(By.CSS_SELECTOR, "a[href*='/ilan/']")
                title = title_link.text.strip()
                listing_data['baslik'] = title
                listing_data['link'] = title_link.get_attribute("href")
                logger.info(f"Başlık: {title[:50]}...")
            except Exception as e:
                logger.warning(f"Başlık alınamadı: {e}")
                listing_data['baslik'] = "N/A"
                listing_data['link'] = "N/A"

            # m² (Brüt) - 3. cell
            try:
                metrekare = cells[2].text.strip()
                listing_data['metrekare'] = metrekare
            except:
                listing_data['metrekare'] = "N/A"

            # Oda Sayısı - 4. cell
            try:
                oda_sayisi = cells[3].text.strip()
                listing_data['oda_sayisi'] = oda_sayisi
            except:
                listing_data['oda_sayisi'] = "N/A"

            # Fiyat - 5. cell
            try:
                fiyat = cells[4].text.strip()
                listing_data['fiyat'] = fiyat
            except:
                listing_data['fiyat'] = "N/A"

            # İlan Tarihi - 6. cell
            try:
                tarih = cells[5].text.strip()
                listing_data['tarih'] = tarih
            except:
                listing_data['tarih'] = "N/A"

            # İlçe/Semt - 7. cell
            try:
                konum = cells[6].text.strip()
                listing_data['konum'] = konum
            except:
                listing_data['konum'] = "N/A"

            # GİZLİ BİLGİLER ARAMA - Ad soyad ve telefon
            try:
                # Tüm satırda gizli attribute'ları ara
                row_html = row.get_attribute("outerHTML")

                # Data attribute'larını ara
                all_elements = row.find_elements(By.CSS_SELECTOR, "*")
                for element in all_elements:
                    # Data attribute'ları kontrol et
                    for attr in ['data-phone', 'data-contact', 'data-owner', 'data-name']:
                        value = element.get_attribute(attr)
                        if value:
                            logger.info(f"Gizli bilgi bulundu {attr}: {value}")

                    # Title attribute'ları kontrol et
                    title_attr = element.get_attribute("title")
                    if title_attr and ("tel" in title_attr.lower() or "phone" in title_attr.lower()):
                        logger.info(f"Title'da telefon bilgisi: {title_attr}")

                # Varsayılan değerler
                listing_data['ad_soyad'] = "N/A"
                listing_data['telefon'] = "N/A"

            except Exception as e:
                logger.warning(f"Gizli bilgi arama hatası: {e}")
                listing_data['ad_soyad'] = "N/A"
                listing_data['telefon'] = "N/A"

            return listing_data

        except Exception as e:
            logger.error(f"Satır işleme hatası: {e}")
            return None
            
    def go_back_to_listing(self):
        """Liste sayfasına geri dön"""
        if not self.driver:
            return False
        try:
            self.driver.back()
            # Sayfanın yüklenmesini bekle
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "table"))
            )
            logger.info("Liste sayfasına geri dönüldü")
            return True
        except:
            logger.error("Liste sayfasına geri dönülemedi")
            return False
            
    def scrape_page(self, page_num):
        """Belirtilen sayfayı scrape et"""
        if not self.driver:
            return

        logger.info(f"Sayfa {page_num} işleniyor...")

        # Bu fonksiyon artık kullanılmıyor - ana run fonksiyonunda direkt işleme yapılıyor
        logger.info("scrape_page fonksiyonu artık kullanılmıyor")
        return
                
    def go_to_next_page(self):
        """Sonraki sayfaya git"""
        if not self.driver:
            return False
        try:
            next_button = self.driver.find_element(By.LINK_TEXT, "Sonraki")
            next_button.click()
            
            # Sayfanın yüklenmesini bekle
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "table"))
            )
            
            logger.info("Sonraki sayfaya geçildi")
            return True
            
        except NoSuchElementException:
            logger.info("Sonraki sayfa bulunamadı")
            return False
        except Exception as e:
            logger.error(f"Sonraki sayfaya geçerken hata: {e}")
            return False
            
    def save_data(self):
        """Verileri dosyaya kaydet"""
        if not self.scraped_data:
            logger.warning("Kaydedilecek veri yok")
            return
            
        # CSV olarak kaydet
        csv_filename = f"sahibinden_ilanlar_{int(time.time())}.csv"
        
        try:
            df = pd.DataFrame(self.scraped_data)
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            logger.info(f"Veriler {csv_filename} dosyasına kaydedildi")
            
            # Excel olarak da kaydet
            excel_filename = csv_filename.replace('.csv', '.xlsx')
            df.to_excel(excel_filename, index=False)
            logger.info(f"Veriler {excel_filename} dosyasına kaydedildi")
            
        except Exception as e:
            logger.error(f"Veri kaydedilirken hata: {e}")
            
    def run(self):
        """Ana scraping fonksiyonu"""
        try:
            # Driver'ı başlat
            self.setup_driver()
            
            # Ana sayfaya git
            if not self.driver:
                logger.error("Driver başlatılamadı")
                return

            # test.py'deki AYNI URL kullan
            url = "https://www.sahibinden.com/satilik/aydin-kusadasi/sahibinden?pagingSize=50"
            logger.info(f"test.py'deki URL ile gidiliyor: {url}")
            self.driver.get(url)

            # Cloudflare bypass
            self.bypass_cloudflare()

            logger.info(f"Şu anki URL: {self.driver.current_url}")
            logger.info(f"Sayfa başlığı: {self.driver.title}")

            # Giriş sayfasına yönlendirildi mi kontrol et
            if "giris" in self.driver.current_url.lower() or "login" in self.driver.current_url.lower():
                logger.error("❌ Giriş sayfasına yönlendirildi, scraping durduruluyor")
                return

            logger.info("✅ İlan sayfasında!")

            # LİSELİ ÇOCUK GİBİ - ÇOK YAVAŞ VE DOĞAL (BOT DETECTION BYPASS)
            logger.info("Sayfa tamamen yüklenmesi bekleniyor (uzun bekleme)...")
            time.sleep(random.uniform(10, 15))  # Çok daha uzun bekleme

            # Çok fazla scroll ve etkileşim (gerçek kullanıcı gibi)
            logger.info("Yoğun sayfa etkileşimi (liseli çocuk gibi)...")

            # 1. Aşağı scroll - yavaş yavaş
            for i in range(5):  # Daha fazla scroll
                scroll_amount = random.randint(150, 300)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(3, 5))  # Çok daha uzun bekleme

            # 2. Yukarı scroll - yavaş yavaş
            for i in range(3):
                scroll_amount = random.randint(100, 200)
                self.driver.execute_script(f"window.scrollBy(0, -{scroll_amount});")
                time.sleep(random.uniform(2, 4))

            # 3. Mouse movements (insan gibi)
            try:
                for i in range(3):
                    x = random.randint(100, 800)
                    y = random.randint(100, 600)
                    ActionChains(self.driver).move_by_offset(x, y).perform()
                    time.sleep(random.uniform(1, 2))
                    ActionChains(self.driver).move_by_offset(-x, -y).perform()
                    time.sleep(random.uniform(1, 2))
            except:
                pass

            # 4. Son uzun bekleme (session güçlendirme)
            logger.info("Session güçlendirme için son bekleme...")
            time.sleep(random.uniform(10, 15))

            # İnsan gibi davran - sayfayı biraz scroll yap
            logger.info("Sayfa scroll yapılıyor...")
            for i in range(2):
                scroll_amount = random.randint(200, 400)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(1, 2))

            # İlan linklerini al ve rastgele sıraya koy
            logger.info("İlan linkleri toplanıyor...")

            # İlan linklerini al - tıklanabilir elementler
            listing_links = self.driver.find_elements(By.CSS_SELECTOR, "table tbody tr td a[href*='/ilan/']")

            # Görünür ve tıklanabilir olanları filtrele
            clickable_links = []
            for link in listing_links:
                try:
                    if link.is_displayed() and link.is_enabled():
                        href = link.get_attribute("href")
                        if href and "/ilan/" in href and "/detay" in href:
                            clickable_links.append(link)
                except:
                    continue

            logger.info(f"✅ {len(clickable_links)} tıklanabilir ilan linki bulundu")

            # İlanları rastgele sıraya koy
            random.shuffle(clickable_links)
            logger.info("İlanlar rastgele sıraya konuldu")

            # LİSELİ ÇOCUK GİBİ - ÇOK YAVAŞ TIKLAMA DENEMESİ
            logger.info("Liseli çocuk gibi çok yavaş ilan tıklama denemesi...")

            # İlk ilan ile test et - ÇOK YAVAŞ
            if clickable_links:
                test_link = clickable_links[0]
                test_href = test_link.get_attribute("href")
                logger.info(f"Test ilan (çok yavaş): {test_href}")

                try:
                    # ÇOK YAVAŞ scroll
                    logger.info("Çok yavaş scroll...")
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", test_link)
                    time.sleep(random.uniform(5, 8))  # Çok uzun bekleme

                    # ÇOK YAVAŞ mouse movements
                    logger.info("Çok yavaş mouse hareketleri...")
                    for i in range(3):
                        ActionChains(self.driver).move_to_element(test_link).perform()
                        time.sleep(random.uniform(2, 4))
                        ActionChains(self.driver).move_by_offset(10, 10).perform()
                        time.sleep(random.uniform(1, 2))
                        ActionChains(self.driver).move_by_offset(-10, -10).perform()
                        time.sleep(random.uniform(1, 2))

                    # ÇOK UZUN bekleme tıklama öncesi
                    logger.info("Tıklama öncesi çok uzun bekleme...")
                    time.sleep(random.uniform(8, 12))

                    # Normal tıklama
                    test_link.click()
                    logger.info("✅ İlana tıklandı (çok yavaş yaklaşım)")

                    # Sayfa yükleme bekleme
                    logger.info("Sayfa yüklenmesi bekleniyor...")
                    time.sleep(random.uniform(5, 8))

                    current_url = self.driver.current_url

                    if "giris" not in current_url.lower() and "login" not in current_url.lower():
                        logger.info("🎉 LOGIN SORMUYOR! Liseli çocuk yaklaşımı başarılı!")
                        logger.info(f"URL: {current_url}")

                        # İlan detaylarını çek
                        listing_data = self.extract_listing_details()
                        if listing_data:
                            logger.info(f"Ad Soyad: {listing_data.get('ad_soyad', 'N/A')}")
                            logger.info(f"Telefon: {listing_data.get('telefon', 'N/A')}")
                    else:
                        logger.warning("❌ Yavaş yaklaşım da login sayfasına yönlendirdi")

                    # ÇOK YAVAŞ geri dönüş
                    logger.info("Çok yavaş geri dönüş...")
                    time.sleep(random.uniform(3, 5))
                    self.driver.back()
                    time.sleep(random.uniform(5, 8))

                except Exception as e:
                    logger.error(f"Yavaş tıklama hatası: {e}")
                    try:
                        self.driver.back()
                        time.sleep(5)
                    except:
                        pass

            # BOT DETECTION ÇÖZÜMÜ: Liste sayfasından direkt bilgileri çek
            logger.info("Liste sayfasından bilgi çekiliyor (ad soyad eksik olacak)...")

            # İlan satırlarını al - Playwright'ta gözlemlenen yapı
            listing_rows = self.driver.find_elements(By.CSS_SELECTOR, "table tbody tr")

            # İlan satırlarını filtrele (başlık satırı ve reklam satırlarını çıkar)
            valid_rows = []
            for row in listing_rows:
                try:
                    # İlan satırı mı kontrol et (7 cell olmalı)
                    cells = row.find_elements(By.CSS_SELECTOR, "td")
                    if len(cells) >= 7:
                        # İkinci cell'de başlık linki var mı?
                        title_links = cells[1].find_elements(By.CSS_SELECTOR, "a[href*='/ilan/']")
                        if title_links:
                            valid_rows.append(row)
                except:
                    continue

            logger.info(f"✅ {len(valid_rows)} geçerli ilan satırı bulundu")

            # İlk 5 ilanı işle - liste sayfasından
            for i, row in enumerate(valid_rows[:5]):
                logger.info(f"\n{i+1}/5 ilan işleniyor...")

                try:
                    listing_data = self.extract_listing_from_row(row)
                    if listing_data:
                        self.scraped_data.append(listing_data)
                        logger.info(f"✅ İlan başarıyla eklendi: {listing_data.get('baslik', 'N/A')[:50]}...")
                    else:
                        logger.warning("❌ İlan bilgileri alınamadı")

                except Exception as e:
                    logger.error(f"İlan satırı işleme hatası: {e}")
                    continue

                # İnsan gibi bekleme
                time.sleep(random.uniform(1, 2))

            # Verileri kaydet
            self.save_data()

            logger.info(f"✅ Scraping tamamlandı! Toplam {len(self.scraped_data)} ilan toplandı.")
            
        except Exception as e:
            logger.error(f"Scraping sırasında hata: {e}")
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("Driver kapatıldı")

if __name__ == "__main__":
    scraper = SahibindenScraper()
    scraper.run()
