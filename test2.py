import os
import time
import random
from bs4 import BeautifulSoup
import undetected_chromedriver as uc
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as ec
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import Action<PERSON>hains

# ---- Locators ----
cloudflare_checkbox = "//div[@id='challenge-stage']/div/label/input[@type='checkbox']"
cookie_accept = "onetrust-accept-btn-handler"
discovery_area = "//div[contains(@class, 'feature-discovery--visible')]"

# ---- Selenium Driver ----
def get_driver():
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"  # kendi Chrome exe
    options = uc.ChromeOptions()
    options.binary_location = chrome_path
    options.add_argument("--guest")  # misafir modu
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument(
        "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/139.0.0.0 Safari/537.36"
    )

    driver = uc.Chrome(options=options)
    driver.maximize_window()

    wait = WebDriverWait(driver, 10)
    short_wait = WebDriverWait(driver, 5)

    driver.execute_cdp_cmd(
        "Network.setExtraHTTPHeaders", 
        {"headers": {"Accept-Language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7"}}
    )

    print("🚀 Chrome driver başlatıldı (Guest mod).")
    return driver, wait, short_wait

# ---- Warm-up ----
def warmup_site(driver, wait, short_wait):
    print("🔥 Warm-up başlatılıyor...")
    driver.get("https://www.sahibinden.com/")

    # Cloudflare checkbox
    try:
        wait.until(ec.frame_to_be_available_and_switch_to_it((By.TAG_NAME, "iframe")))
        wait.until(ec.element_to_be_clickable((By.XPATH, cloudflare_checkbox))).click()
        driver.switch_to.parent_frame()
        print("☑️ Cloudflare checkbox tıklandı.")
    except:
        driver.switch_to.default_content()

    # Cookie
    try:
        cookie_btn = short_wait.until(ec.element_to_be_clickable((By.ID, cookie_accept)))
        driver.execute_script("arguments[0].click();", cookie_btn)
        print("🍪 Cookie banner kabul edildi.")
    except:
        pass

    # Discovery popup
    try:
        short_wait.until(ec.element_to_be_clickable((By.XPATH, discovery_area))).click()
        print("🔍 Discovery popup kapatıldı.")
    except:
        pass

    time.sleep(random.uniform(2, 4))
    print("🔥 Warm-up tamamlandı.")

# ---- Listing Page ----
def open_listing_page(driver, wait, short_wait, output_html):
    print("🌐 Liste sayfası açılıyor...")
    driver.get("https://www.sahibinden.com/satilik/aydin-kusadasi/sahibinden?pagingSize=50")

    wait.until(ec.presence_of_element_located((By.TAG_NAME, "body")))
    html = driver.page_source
    with open(output_html, "w", encoding="utf-8") as f:
        f.write(html)

    print(f"✅ Liste sayfası kaydedildi -> {output_html}")
    return html

# ---- Link Extract ----
def extract_links_from_html(html):
    print("🔗 İlan linkleri çıkarılıyor...")
    soup = BeautifulSoup(html, "html.parser")
    links = []
    for a in soup.find_all("a", href=True):
        href = a["href"]
        if href.startswith("/ilan/") and "/detay" in href:
            links.append("https://www.sahibinden.com" + href)
    links = list(dict.fromkeys(links))
    print(f"✅ Toplam {len(links)} ilan bulundu.")
    return links

# ---- Listing Downloader (Random) ----
def download_listing_pages(driver, wait, links, folder="ilanlar"):
    if not os.path.exists(folder):
        os.makedirs(folder)
    if not os.path.exists("screenshots"):
        os.makedirs("screenshots")

    print("🚀 İlan detayları indirilmeye başlanıyor...")

    # Önce tüm linkleri ekrana bas
    print("🔗 Tüm ilan linkleri:")
    for i, url in enumerate(links, start=1):
        print(f"{i}. {url}")

    # Random sıralama
    random_links = links[:]
    random.shuffle(random_links)

    for idx, url in enumerate(random_links, start=1):
        try:
            # Sayfadaki tüm ilan linklerini bul, href ile eşleşeni seç
            elements = driver.find_elements(By.CLASS_NAME, "classifiedTitle")
            link_element = next((el for el in elements if el.get_attribute("href") == url), None)
            if not link_element:
                print(f"❌ Link bulunamadı: {url}")
                continue

            # Scroll ve click
            driver.execute_script(
                "arguments[0].scrollIntoView({behavior:'smooth', block:'center'});",
                link_element
            )

            
            # Cookie
            try:
                cookie_btn = short_wait.until(ec.element_to_be_clickable((By.ID, cookie_accept)))
                driver.execute_script("arguments[0].click();", cookie_btn)
                print("🍪 Cookie banner kabul edildi.")
            except:
                pass

            time.sleep(random.uniform(1, 2))
            
            ActionChains(driver).move_to_element(link_element).pause(random.uniform(0.5, 1.0)).click().perform()
            print(f"[{idx}/{len(random_links)}] {url} sayfası açıldı.")

            wait.until(ec.presence_of_element_located((By.TAG_NAME, "body")))

            # HTML kaydet
            html = driver.page_source
            filename = os.path.join(folder, f"ilan_{idx}.html")
            with open(filename, "w", encoding="utf-8") as f:
                f.write(html)
            print(f"[{idx}/{len(random_links)}] Kaydedildi -> {filename}")

            # Rastgele bekleme
            time.sleep(random.uniform(2, 5))

            # Liste sayfasına geri dön
            driver.back()
            print(f"[{idx}/{len(random_links)}] Liste sayfasına dönüldü.")
            time.sleep(random.uniform(1, 2))

        except Exception as e:
            print(f"❌ Hata: {url} -> {e}")
            try:
                screenshot_path = os.path.join("screenshots", f"error_{idx}.png")
                driver.save_screenshot(screenshot_path)
                print(f"⚠️ Screenshot kaydedildi: {screenshot_path}")
            except:
                print("Screenshot alınamadı!")

    print("✅ Tüm ilanlar indirildi.")

# ---- Main ----
def main():
    OUTPUT_HTML = "sahibinden_kusadasi.html"
    driver, wait, short_wait = get_driver()
    
    try:
        # Warm-up
        warmup_site(driver, wait, short_wait)

        # Liste sayfasını aç → HTML kaydet
        html = open_listing_page(driver, wait, short_wait, OUTPUT_HTML)

        # İlan linklerini çıkar
        links = extract_links_from_html(html)

        # İlan detaylarını indir (random)
        download_listing_pages(driver, wait, links, folder="ilanlar")

    finally:
        print("Program bitti, browser açık kalacak. Kapatmak için CTRL+C yapabilirsiniz.")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("Kapatılıyor...")

if __name__ == "__main__":
    main()
