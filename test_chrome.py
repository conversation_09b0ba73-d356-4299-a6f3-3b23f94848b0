from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

try:
    # Playwright tarzı Chrome options
    print("Chrome başlatılıyor (Playwright tarzı)...")
    chrome_options = Options()

    # Playwright'ın kulland<PERSON> user agent'a benzer
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36")

    # Bot detection'ı minimize et
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")

    # Playwright gibi davran
    chrome_options.add_argument("--no-first-run")
    chrome_options.add_argument("--no-default-browser-check")
    chrome_options.add_argument("--disable-default-apps")

    driver = webdriver.Chrome(options=chrome_options)

    # Webdriver özelliğini gizle
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    print("Chrome başlatıldı")
    
    # Sahibinden'e git
    url = "https://www.sahibinden.com/satilik-daire/aydin/sahibinden"
    print(f"Sayfaya gidiliyor: {url}")
    driver.get(url)
    
    print("Sayfa yüklendi, Cloudflare challenge kontrolü...")

    # Cloudflare challenge'ını otomatik geç
    for i in range(30):  # 30 saniye boyunca kontrol et
        time.sleep(1)
        title = driver.title
        print(f"Saniye {i+1}: {title}")

        # Cloudflare iframe'ini ara ve checkbox'ı tıkla
        try:
            # Iframe'leri kontrol et
            iframes = driver.find_elements("css selector", "iframe")
            print(f"{len(iframes)} iframe bulundu")

            for iframe in iframes:
                src = iframe.get_attribute("src")
                if src and ("turnstile" in src or "cloudflare" in src):
                    print(f"Cloudflare iframe bulundu: {src}")

                    # Iframe'e geç
                    driver.switch_to.frame(iframe)

                    # Checkbox'ı ara
                    checkbox_selectors = [
                        "input[type='checkbox']",
                        "label",
                        "div[role='checkbox']",
                        ".cb-i",
                        "#challenge-stage"
                    ]

                    for selector in checkbox_selectors:
                        elements = driver.find_elements("css selector", selector)
                        if elements:
                            print(f"Checkbox bulundu: {selector}")
                            elements[0].click()
                            print("Checkbox tıklandı!")
                            time.sleep(3)
                            break

                    # Ana frame'e geri dön
                    driver.switch_to.default_content()
                    break

            # Eğer iframe yoksa, direkt sayfada ara
            if not iframes:
                checkbox_selectors = [
                    "input[type='checkbox']",
                    ".cf-turnstile",
                    "#challenge-form",
                    "[data-testid='cf-turnstile']"
                ]

                for selector in checkbox_selectors:
                    elements = driver.find_elements("css selector", selector)
                    if elements:
                        print(f"Direkt checkbox bulundu: {selector}")
                        elements[0].click()
                        print("Checkbox tıklandı!")
                        time.sleep(3)
                        break

        except Exception as e:
            print(f"Checkbox tıklama hatası: {e}")

        if "Bir dakika lütfen" not in title and "Just a moment" not in title:
            print("Cloudflare geçildi!")
            break
    else:
        print("Cloudflare geçilemedi, ama devam ediliyor...")
    
    print("Bekleme tamamlandı!")
    print(f"Şu anki URL: {driver.current_url}")
    print(f"Sayfa başlığı: {driver.title}")

    # İlan detayına gitmeyi dene
    print("\nİlan detayına gitmeyi deniyorum...")

    # İlan linklerini al
    listing_links = driver.find_elements(By.CSS_SELECTOR, "table tbody tr td a[href*='/ilan/']")
    print(f"{len(listing_links)} ilan linki bulundu")

    if listing_links:
        # İlk ilana tıkla
        first_link = listing_links[0]
        href = first_link.get_attribute("href")
        print(f"İlk ilana tıklanıyor: {href}")

        first_link.click()
        time.sleep(5)

        print(f"Yeni URL: {driver.current_url}")
        print(f"Yeni başlık: {driver.title}")

        if "giris" in driver.current_url.lower():
            print("❌ Login sayfasına yönlendirildi!")
        else:
            print("✅ İlan detayına ulaşıldı!")

    # Browser'ı açık tut
    input("Browser'ı kapatmak için Enter'a basın...")
    
    driver.quit()
    print("Browser kapatıldı")
    
except Exception as e:
    print(f"Hata: {e}")
    try:
        driver.quit()
    except:
        pass
